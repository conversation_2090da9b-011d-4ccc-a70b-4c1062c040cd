"""
异步视频处理器
支持后台视频处理任务，包括片头片尾添加、进度监控、任务取消等功能
"""

import asyncio
import os
import time
import uuid
import json
import logging
import subprocess
import re
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class VideoTaskStatus(Enum):
    """视频任务状态"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消

class VideoTaskType(Enum):
    """视频任务类型"""
    INTRO_OUTRO = "intro_outro"  # 片头片尾
    MERGE = "merge"              # 视频合并
    CLIP = "clip"                # 视频裁剪
    ACCELERATION = "acceleration" # 视频加速
    AUDIO_SEPARATION = "audio_separation"  # 音频分离

@dataclass
class VideoTaskProgress:
    """视频任务进度"""
    current_frame: int = 0
    total_frames: int = 0
    current_time: float = 0.0
    total_time: float = 0.0
    percentage: float = 0.0
    fps: float = 0.0
    speed: float = 0.0
    eta_seconds: int = 0

@dataclass
class VideoTask:
    """视频处理任务"""
    task_id: str
    task_type: VideoTaskType
    status: VideoTaskStatus
    input_files: List[str]
    output_file: str
    parameters: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: VideoTaskProgress = None
    error_message: Optional[str] = None
    process: Optional[asyncio.subprocess.Process] = None
    
    def __post_init__(self):
        if self.progress is None:
            self.progress = VideoTaskProgress()

class AsyncVideoProcessor:
    """异步视频处理器"""
    
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, VideoTask] = {}
        self.task_queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        self._lock = threading.Lock()
        
    async def start(self):
        """启动处理器"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"异步视频处理器已启动，工作线程数: {self.max_concurrent_tasks}")
    
    async def stop(self):
        """停止处理器"""
        self.is_running = False
        
        # 取消所有正在运行的任务
        for task in self.tasks.values():
            if task.status == VideoTaskStatus.RUNNING and task.process:
                try:
                    task.process.terminate()
                    await asyncio.sleep(1)
                    if task.process.returncode is None:
                        task.process.kill()
                    task.status = VideoTaskStatus.CANCELLED
                except Exception as e:
                    logger.error(f"取消任务失败: {e}")
        
        # 停止工作线程
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("异步视频处理器已停止")
    
    async def submit_intro_outro_task(self, video_path: str, output_path: str,
                                     intro_path: Optional[str] = None,
                                     outro_path: Optional[str] = None,
                                     transition_effect: str = 'fade',
                                     transition_duration: float = 1.0,
                                     output_quality: str = 'medium',
                                     progress_callback: Optional[Callable] = None) -> str:
        """提交片头片尾处理任务"""
        
        task_id = str(uuid.uuid4())
        
        # 验证输入文件
        input_files = [video_path]
        if intro_path:
            input_files.append(intro_path)
        if outro_path:
            input_files.append(outro_path)
        
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"输入文件不存在: {file_path}")
        
        # 创建任务
        task = VideoTask(
            task_id=task_id,
            task_type=VideoTaskType.INTRO_OUTRO,
            status=VideoTaskStatus.PENDING,
            input_files=input_files,
            output_file=output_path,
            parameters={
                'video_path': video_path,
                'intro_path': intro_path,
                'outro_path': outro_path,
                'transition_effect': transition_effect,
                'transition_duration': transition_duration,
                'output_quality': output_quality,
                'progress_callback': progress_callback
            },
            created_at=datetime.now()
        )
        
        with self._lock:
            self.tasks[task_id] = task
        
        # 添加到队列
        await self.task_queue.put(task_id)
        
        logger.info(f"提交片头片尾任务: {task_id}, 输入: {video_path}, 输出: {output_path}")
        return task_id
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self._lock:
            task = self.tasks.get(task_id)
        
        if not task:
            return None
        
        return {
            'task_id': task.task_id,
            'task_type': task.task_type.value,
            'status': task.status.value,
            'input_files': task.input_files,
            'output_file': task.output_file,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'progress': asdict(task.progress),
            'error_message': task.error_message
        }
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            task = self.tasks.get(task_id)
        
        if not task:
            return False
        
        if task.status == VideoTaskStatus.RUNNING and task.process:
            try:
                task.process.terminate()
                await asyncio.sleep(1)
                if task.process.returncode is None:
                    task.process.kill()
                task.status = VideoTaskStatus.CANCELLED
                task.completed_at = datetime.now()
                logger.info(f"任务已取消: {task_id}")
                return True
            except Exception as e:
                logger.error(f"取消任务失败: {task_id}, 错误: {e}")
                return False
        elif task.status == VideoTaskStatus.PENDING:
            task.status = VideoTaskStatus.CANCELLED
            task.completed_at = datetime.now()
            logger.info(f"等待中的任务已取消: {task_id}")
            return True
        
        return False
    
    async def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        with self._lock:
            tasks = list(self.tasks.values())
        
        return [
            {
                'task_id': task.task_id,
                'task_type': task.task_type.value,
                'status': task.status.value,
                'input_files': task.input_files,
                'output_file': task.output_file,
                'created_at': task.created_at.isoformat(),
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'progress': asdict(task.progress),
                'error_message': task.error_message
            }
            for task in tasks
        ]
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"视频处理工作线程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 等待任务
                task_id = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                with self._lock:
                    task = self.tasks.get(task_id)
                
                if not task or task.status != VideoTaskStatus.PENDING:
                    continue
                
                # 处理任务
                await self._process_task(task, worker_name)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"工作线程异常: {worker_name}, 错误: {e}")
        
        logger.info(f"视频处理工作线程停止: {worker_name}")
    
    async def _process_task(self, task: VideoTask, worker_name: str):
        """处理单个任务"""
        async with self.semaphore:
            try:
                logger.info(f"{worker_name} 开始处理任务: {task.task_id}")
                
                task.status = VideoTaskStatus.RUNNING
                task.started_at = datetime.now()
                
                if task.task_type == VideoTaskType.INTRO_OUTRO:
                    await self._process_intro_outro_task(task)
                else:
                    raise NotImplementedError(f"不支持的任务类型: {task.task_type}")
                
                if task.status == VideoTaskStatus.RUNNING:
                    task.status = VideoTaskStatus.COMPLETED
                    task.completed_at = datetime.now()
                    task.progress.percentage = 100.0
                    logger.info(f"任务完成: {task.task_id}")
                
            except Exception as e:
                task.status = VideoTaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error_message = str(e)
                logger.error(f"任务失败: {task.task_id}, 错误: {e}")

    async def _process_intro_outro_task(self, task: VideoTask):
        """处理片头片尾任务"""
        params = task.parameters
        video_path = params['video_path']
        intro_path = params.get('intro_path')
        outro_path = params.get('outro_path')
        output_path = task.output_file
        transition_effect = params.get('transition_effect', 'fade')
        transition_duration = params.get('transition_duration', 1.0)
        output_quality = params.get('output_quality', 'medium')
        progress_callback = params.get('progress_callback')

        # 获取视频信息
        video_info = await self._get_video_info(video_path)
        if not video_info:
            raise Exception(f"无法获取视频信息: {video_path}")

        main_width = video_info.get('width', 1920)
        main_height = video_info.get('height', 1080)
        main_fps = video_info.get('fps', 30)
        main_duration = video_info.get('duration', 0)

        # 计算总时长
        total_duration = main_duration
        if intro_path:
            intro_info = await self._get_video_info(intro_path)
            total_duration += intro_info.get('duration', 0) if intro_info else 0
        if outro_path:
            outro_info = await self._get_video_info(outro_path)
            total_duration += outro_info.get('duration', 0) if outro_info else 0

        task.progress.total_time = total_duration

        # 构建ffmpeg命令
        cmd = ['ffmpeg', '-y', '-progress', 'pipe:1']

        # 添加输入文件
        if intro_path:
            cmd.extend(['-i', intro_path])
        cmd.extend(['-i', video_path])
        if outro_path:
            cmd.extend(['-i', outro_path])

        # 构建滤镜
        filter_complex = self._build_intro_outro_filter(
            intro_path, outro_path, main_width, main_height, main_fps
        )

        if filter_complex:
            cmd.extend(['-filter_complex', filter_complex])
            cmd.extend(['-map', '[outv]', '-map', '[outa]'])

        # 编码参数
        cmd.extend(['-c:v', 'libx264', '-preset', 'fast'])

        # 质量设置
        if output_quality == 'high':
            cmd.extend(['-crf', '18'])
        elif output_quality == 'medium':
            cmd.extend(['-crf', '23'])
        else:
            cmd.extend(['-crf', '28'])

        cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
        cmd.append(output_path)

        logger.info(f"执行ffmpeg命令: {' '.join(cmd)}")

        # 启动进程
        task.process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        # 监控进度
        await self._monitor_ffmpeg_progress(task, progress_callback)

        # 等待完成
        await task.process.wait()

        if task.process.returncode != 0:
            stderr_output = await task.process.stderr.read()
            error_msg = stderr_output.decode('utf-8', errors='ignore')
            raise Exception(f"ffmpeg处理失败: {error_msg}")

    def _build_intro_outro_filter(self, intro_path: Optional[str], outro_path: Optional[str],
                                 width: int, height: int, fps: int) -> str:
        """构建片头片尾滤镜"""
        if intro_path and outro_path:
            return (
                f"[0:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[intro_scaled];"
                f"[1:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[main_scaled];"
                f"[2:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[outro_scaled];"
                f"[intro_scaled][0:a][main_scaled][1:a][outro_scaled][2:a]concat=n=3:v=1:a=1[outv][outa]"
            )
        elif intro_path:
            return (
                f"[0:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[intro_scaled];"
                f"[1:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[main_scaled];"
                f"[intro_scaled][0:a][main_scaled][1:a]concat=n=2:v=1:a=1[outv][outa]"
            )
        elif outro_path:
            return (
                f"[0:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[main_scaled];"
                f"[1:v]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,fps={fps},setsar=1[outro_scaled];"
                f"[main_scaled][0:a][outro_scaled][1:a]concat=n=2:v=1:a=1[outv][outa]"
            )
        else:
            return "[0:v]setsar=1[outv]; [0:a]copy[outa]"

    async def _get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_entries',
                'format=duration:stream=width,height,r_frame_rate', video_path
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, _ = await process.communicate()

            if process.returncode == 0:
                import json
                data = json.loads(stdout.decode('utf-8'))

                # 提取信息
                info = {}
                if 'format' in data and 'duration' in data['format']:
                    info['duration'] = float(data['format']['duration'])

                if 'streams' in data:
                    for stream in data['streams']:
                        if stream.get('codec_type') == 'video':
                            info['width'] = stream.get('width', 1920)
                            info['height'] = stream.get('height', 1080)

                            # 解析帧率
                            r_frame_rate = stream.get('r_frame_rate', '30/1')
                            if '/' in r_frame_rate:
                                num, den = r_frame_rate.split('/')
                                info['fps'] = float(num) / float(den)
                            else:
                                info['fps'] = float(r_frame_rate)
                            break

                return info

        except Exception as e:
            logger.error(f"获取视频信息失败: {video_path}, 错误: {e}")

        return None

    async def _monitor_ffmpeg_progress(self, task: VideoTask, progress_callback: Optional[Callable]):
        """监控ffmpeg进度"""
        if not task.process or not task.process.stdout:
            return

        try:
            while True:
                line = await task.process.stdout.readline()
                if not line:
                    break

                line = line.decode('utf-8').strip()
                if not line:
                    continue

                # 解析进度信息
                if line.startswith('out_time_ms='):
                    time_ms = int(line.split('=')[1])
                    current_time = time_ms / 1000000.0  # 转换为秒

                    if task.progress.total_time > 0:
                        percentage = min(100.0, (current_time / task.progress.total_time) * 100)
                        task.progress.current_time = current_time
                        task.progress.percentage = percentage

                        # 调用进度回调
                        if progress_callback:
                            try:
                                if asyncio.iscoroutinefunction(progress_callback):
                                    await progress_callback(task.task_id, percentage, current_time, task.progress.total_time)
                                else:
                                    progress_callback(task.task_id, percentage, current_time, task.progress.total_time)
                            except Exception as e:
                                logger.error(f"进度回调异常: {e}")

                elif line.startswith('fps='):
                    fps_match = re.search(r'fps=(\d+\.?\d*)', line)
                    if fps_match:
                        task.progress.fps = float(fps_match.group(1))

                elif line.startswith('speed='):
                    speed_match = re.search(r'speed=(\d+\.?\d*)x', line)
                    if speed_match:
                        task.progress.speed = float(speed_match.group(1))

        except Exception as e:
            logger.error(f"监控进度异常: {e}")

# 全局实例
_video_processor = None

async def get_video_processor() -> AsyncVideoProcessor:
    """获取视频处理器实例"""
    global _video_processor
    if _video_processor is None:
        _video_processor = AsyncVideoProcessor()
        await _video_processor.start()
    return _video_processor
