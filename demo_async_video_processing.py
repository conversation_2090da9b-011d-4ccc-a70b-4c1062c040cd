#!/usr/bin/env python3
"""
异步视频处理系统演示脚本
展示如何使用新的异步视频处理功能
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

class AsyncVideoProcessingDemo:
    """异步视频处理演示类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def submit_intro_outro_task(self, video_path: str, output_path: str, 
                                     intro_path: str = None, outro_path: str = None,
                                     quality: str = "medium") -> str:
        """提交片头片尾处理任务"""
        url = f"{self.base_url}/api/v1/async-video/intro-outro"
        
        payload = {
            "video_path": video_path,
            "output_path": output_path,
            "transition_effect": "fade",
            "transition_duration": 1.0,
            "output_quality": quality
        }
        
        if intro_path:
            payload["intro_path"] = intro_path
        if outro_path:
            payload["outro_path"] = outro_path
        
        async with self.session.post(url, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("task_id")
            else:
                error_text = await response.text()
                raise Exception(f"提交任务失败: {response.status} - {error_text}")
    
    async def submit_batch_intro_outro_task(self, folder_path: str, 
                                           intro_path: str = None, outro_path: str = None,
                                           selected_files: list = None,
                                           output_folder: str = None) -> list:
        """提交批量片头片尾处理任务"""
        url = f"{self.base_url}/api/v1/async-video/batch-intro-outro"
        
        payload = {
            "folder_path": folder_path,
            "transition_effect": "fade",
            "transition_duration": 1.0,
            "output_quality": "medium",
            "max_concurrent": 3,
            "overwrite_original": False,
            "output_suffix": "_with_intro_outro"
        }
        
        if intro_path:
            payload["intro_path"] = intro_path
        if outro_path:
            payload["outro_path"] = outro_path
        if selected_files:
            payload["selected_files"] = selected_files
        if output_folder:
            payload["output_folder"] = output_folder
        
        async with self.session.post(url, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("task_ids", [])
            else:
                error_text = await response.text()
                raise Exception(f"提交批量任务失败: {response.status} - {error_text}")
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        url = f"{self.base_url}/api/v1/async-video/task/{task_id}"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_text = await response.text()
                raise Exception(f"获取任务状态失败: {response.status} - {error_text}")
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        url = f"{self.base_url}/api/v1/async-video/task/{task_id}"
        
        async with self.session.delete(url) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("success", False)
            else:
                error_text = await response.text()
                raise Exception(f"取消任务失败: {response.status} - {error_text}")
    
    async def get_all_tasks(self) -> list:
        """获取所有任务"""
        url = f"{self.base_url}/api/v1/async-video/tasks"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("tasks", [])
            else:
                error_text = await response.text()
                raise Exception(f"获取任务列表失败: {response.status} - {error_text}")
    
    async def monitor_task_progress(self, task_id: str, check_interval: int = 2):
        """监控任务进度"""
        print(f"开始监控任务: {task_id}")
        
        while True:
            try:
                status = await self.get_task_status(task_id)
                
                task_status = status.get("status", "unknown")
                progress = status.get("progress", {})
                percentage = progress.get("percentage", 0)
                current_time = progress.get("current_time", 0)
                total_time = progress.get("total_time", 0)
                fps = progress.get("fps", 0)
                speed = progress.get("speed", 0)
                
                print(f"任务状态: {task_status}")
                print(f"进度: {percentage:.1f}%")
                if total_time > 0:
                    print(f"时间: {current_time:.1f}s / {total_time:.1f}s")
                if fps > 0:
                    print(f"帧率: {fps:.1f} fps")
                if speed > 0:
                    print(f"处理速度: {speed:.1f}x")
                
                if task_status in ["completed", "failed", "cancelled"]:
                    print(f"任务完成，最终状态: {task_status}")
                    if task_status == "failed":
                        error_msg = status.get("error_message", "未知错误")
                        print(f"错误信息: {error_msg}")
                    break
                
                print("-" * 50)
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                print(f"监控任务时出错: {e}")
                break

async def demo_single_task():
    """演示单个任务处理"""
    print("=== 单个任务处理演示 ===")
    
    async with AsyncVideoProcessingDemo() as demo:
        try:
            # 提交任务
            task_id = await demo.submit_intro_outro_task(
                video_path="/path/to/video.mp4",
                output_path="/path/to/output.mp4",
                intro_path="/path/to/intro.mp4",
                outro_path="/path/to/outro.mp4",
                quality="high"
            )
            
            print(f"任务已提交: {task_id}")
            
            # 监控进度
            await demo.monitor_task_progress(task_id)
            
        except Exception as e:
            print(f"演示失败: {e}")

async def demo_batch_tasks():
    """演示批量任务处理"""
    print("=== 批量任务处理演示 ===")
    
    async with AsyncVideoProcessingDemo() as demo:
        try:
            # 提交批量任务
            task_ids = await demo.submit_batch_intro_outro_task(
                folder_path="/path/to/videos",
                intro_path="/path/to/intro.mp4",
                outro_path="/path/to/outro.mp4",
                selected_files=["video1.mp4", "video2.mp4", "video3.mp4"],
                output_folder="/path/to/output"
            )
            
            print(f"批量任务已提交: {len(task_ids)} 个任务")
            for i, task_id in enumerate(task_ids, 1):
                print(f"  任务 {i}: {task_id}")
            
            # 监控所有任务
            print("\n开始监控所有任务...")
            
            while True:
                all_tasks = await demo.get_all_tasks()
                
                # 过滤出我们提交的任务
                our_tasks = [task for task in all_tasks if task["task_id"] in task_ids]
                
                completed_count = 0
                for task in our_tasks:
                    status = task["status"]
                    progress = task["progress"]["percentage"]
                    print(f"任务 {task['task_id'][:8]}...: {status} ({progress:.1f}%)")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        completed_count += 1
                
                if completed_count == len(task_ids):
                    print("所有任务已完成!")
                    break
                
                print("-" * 50)
                await asyncio.sleep(3)
                
        except Exception as e:
            print(f"批量演示失败: {e}")

async def demo_task_management():
    """演示任务管理功能"""
    print("=== 任务管理功能演示 ===")
    
    async with AsyncVideoProcessingDemo() as demo:
        try:
            # 提交一个任务
            task_id = await demo.submit_intro_outro_task(
                video_path="/path/to/video.mp4",
                output_path="/path/to/output.mp4",
                intro_path="/path/to/intro.mp4"
            )
            
            print(f"任务已提交: {task_id}")
            
            # 等待一会儿
            await asyncio.sleep(2)
            
            # 获取任务状态
            status = await demo.get_task_status(task_id)
            print(f"任务状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
            
            # 取消任务
            success = await demo.cancel_task(task_id)
            print(f"取消任务: {'成功' if success else '失败'}")
            
            # 再次获取状态
            status = await demo.get_task_status(task_id)
            print(f"取消后状态: {status['status']}")
            
        except Exception as e:
            print(f"任务管理演示失败: {e}")

def print_usage_guide():
    """打印使用指南"""
    print("""
=== 异步视频处理系统使用指南 ===

1. 系统特性:
   ✅ 真正的后台异步处理，不阻塞主进程
   ✅ 实时进度监控和状态更新
   ✅ 支持任务取消和错误处理
   ✅ 批量处理支持
   ✅ 完整的RESTful API

2. 主要API端点:
   - POST /api/v1/async-video/intro-outro          # 提交单个任务
   - POST /api/v1/async-video/batch-intro-outro    # 提交批量任务
   - GET  /api/v1/async-video/task/{task_id}       # 查询任务状态
   - DELETE /api/v1/async-video/task/{task_id}     # 取消任务
   - GET  /api/v1/async-video/tasks                # 获取所有任务

3. 任务状态:
   - pending: 等待中
   - running: 运行中
   - completed: 已完成
   - failed: 失败
   - cancelled: 已取消

4. 使用建议:
   - 合理设置并发数量避免系统过载
   - 定期检查任务状态获取进度更新
   - 处理失败任务的错误信息
   - 使用批量接口处理大量文件

5. 向后兼容:
   - 原有同步接口仍然可用
   - 建议迁移到异步接口获得更好体验

详细文档请参考: docs/async_video_processing_guide.md
""")

async def main():
    """主函数"""
    print("异步视频处理系统演示")
    print("=" * 50)
    
    # 打印使用指南
    print_usage_guide()
    
    print("\n注意: 以下演示需要ThunderHub服务正在运行")
    print("请确保服务已启动并且可以访问 http://localhost:8000")
    print("\n演示使用的文件路径是示例路径，实际使用时请替换为真实路径")
    
    # 演示功能（注释掉避免在没有服务的情况下出错）
    # await demo_single_task()
    # await demo_batch_tasks() 
    # await demo_task_management()
    
    print("\n演示完成!")

if __name__ == "__main__":
    asyncio.run(main())
