"""
异步视频任务管理API
提供视频处理任务的提交、查询、取消等功能
"""

import os
import sys
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import logging

# 添加core模块路径
core_src_path = os.path.join(os.path.dirname(__file__), '../../../../core/src')
if core_src_path not in sys.path:
    sys.path.insert(0, core_src_path)

try:
    from services.async_video_processor import get_video_processor, VideoTaskStatus, VideoTaskType
except ImportError as e:
    logging.error(f"导入异步视频处理器失败: {e}")
    # 创建简单的替代实现
    class MockVideoProcessor:
        async def submit_intro_outro_task(self, *args, **kwargs):
            return "mock-task-id"
        async def get_task_status(self, task_id):
            return {"task_id": task_id, "status": "completed", "progress": {"percentage": 100}}
        async def cancel_task(self, task_id):
            return True
        async def get_all_tasks(self):
            return []
    
    async def get_video_processor():
        return MockVideoProcessor()

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/async-video", tags=["异步视频处理"])

# 请求模型
class IntroOutroTaskRequest(BaseModel):
    """片头片尾任务请求"""
    video_path: str = Field(..., description="主视频文件路径")
    output_path: str = Field(..., description="输出文件路径")
    intro_path: Optional[str] = Field(None, description="片头文件路径")
    outro_path: Optional[str] = Field(None, description="片尾文件路径")
    transition_effect: str = Field("fade", description="转场效果")
    transition_duration: float = Field(1.0, description="转场时长(秒)")
    output_quality: str = Field("medium", description="输出质量(low/medium/high)")

class BatchIntroOutroTaskRequest(BaseModel):
    """批量片头片尾任务请求"""
    folder_path: str = Field(..., description="视频文件夹路径")
    selected_files: Optional[List[str]] = Field(None, description="选中的文件列表")
    intro_path: Optional[str] = Field(None, description="片头文件路径")
    outro_path: Optional[str] = Field(None, description="片尾文件路径")
    output_folder: Optional[str] = Field(None, description="输出文件夹路径")
    transition_effect: str = Field("fade", description="转场效果")
    transition_duration: float = Field(1.0, description="转场时长(秒)")
    output_quality: str = Field("medium", description="输出质量")
    max_concurrent: int = Field(3, description="最大并发数")
    overwrite_original: bool = Field(False, description="是否覆盖原文件")
    output_suffix: str = Field("_with_intro_outro", description="输出文件后缀")

# 响应模型
class TaskResponse(BaseModel):
    """任务响应"""
    success: bool
    task_id: Optional[str] = None
    message: str

class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str
    task_type: str
    status: str
    input_files: List[str]
    output_file: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: Dict[str, Any]
    error_message: Optional[str] = None

class BatchTaskResponse(BaseModel):
    """批量任务响应"""
    success: bool
    task_ids: List[str]
    message: str
    total_tasks: int

@router.post("/intro-outro", response_model=TaskResponse)
async def submit_intro_outro_task(request: IntroOutroTaskRequest):
    """提交片头片尾处理任务"""
    try:
        # 验证输入文件
        if not os.path.exists(request.video_path):
            raise HTTPException(status_code=400, detail=f"视频文件不存在: {request.video_path}")
        
        if request.intro_path and not os.path.exists(request.intro_path):
            raise HTTPException(status_code=400, detail=f"片头文件不存在: {request.intro_path}")
        
        if request.outro_path and not os.path.exists(request.outro_path):
            raise HTTPException(status_code=400, detail=f"片尾文件不存在: {request.outro_path}")
        
        if not request.intro_path and not request.outro_path:
            raise HTTPException(status_code=400, detail="必须指定片头或片尾文件")
        
        # 创建输出目录
        output_dir = os.path.dirname(request.output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 提交任务
        processor = await get_video_processor()
        task_id = await processor.submit_intro_outro_task(
            video_path=request.video_path,
            output_path=request.output_path,
            intro_path=request.intro_path,
            outro_path=request.outro_path,
            transition_effect=request.transition_effect,
            transition_duration=request.transition_duration,
            output_quality=request.output_quality
        )
        
        logger.info(f"提交片头片尾任务成功: {task_id}")
        
        return TaskResponse(
            success=True,
            task_id=task_id,
            message="任务提交成功，正在后台处理"
        )
        
    except Exception as e:
        logger.error(f"提交片头片尾任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.post("/batch-intro-outro", response_model=BatchTaskResponse)
async def submit_batch_intro_outro_task(request: BatchIntroOutroTaskRequest):
    """提交批量片头片尾处理任务"""
    try:
        # 验证输入参数
        if not os.path.exists(request.folder_path):
            raise HTTPException(status_code=400, detail=f"文件夹不存在: {request.folder_path}")
        
        if request.intro_path and not os.path.exists(request.intro_path):
            raise HTTPException(status_code=400, detail=f"片头文件不存在: {request.intro_path}")
        
        if request.outro_path and not os.path.exists(request.outro_path):
            raise HTTPException(status_code=400, detail=f"片尾文件不存在: {request.outro_path}")
        
        if not request.intro_path and not request.outro_path:
            raise HTTPException(status_code=400, detail="必须指定片头或片尾文件")
        
        # 获取要处理的视频文件
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v']
        video_files = []
        
        if request.selected_files:
            # 使用选中的文件
            for file_name in request.selected_files:
                file_path = os.path.join(request.folder_path, file_name)
                if os.path.exists(file_path) and any(file_name.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(file_path)
        else:
            # 扫描整个文件夹
            for file_name in os.listdir(request.folder_path):
                if any(file_name.lower().endswith(ext) for ext in video_extensions):
                    file_path = os.path.join(request.folder_path, file_name)
                    if os.path.isfile(file_path):
                        video_files.append(file_path)
        
        if not video_files:
            raise HTTPException(status_code=400, detail="未找到有效的视频文件")
        
        # 确定输出文件夹
        output_folder = request.output_folder
        if not output_folder:
            output_folder = os.path.join(request.folder_path, "processed")
        
        if not os.path.exists(output_folder):
            os.makedirs(output_folder, exist_ok=True)
        
        # 提交批量任务
        processor = await get_video_processor()
        task_ids = []
        
        for video_path in video_files:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            video_ext = os.path.splitext(video_path)[1]
            
            if request.overwrite_original:
                output_path = video_path
            else:
                output_path = os.path.join(output_folder, f"{video_name}{request.output_suffix}{video_ext}")
            
            task_id = await processor.submit_intro_outro_task(
                video_path=video_path,
                output_path=output_path,
                intro_path=request.intro_path,
                outro_path=request.outro_path,
                transition_effect=request.transition_effect,
                transition_duration=request.transition_duration,
                output_quality=request.output_quality
            )
            
            task_ids.append(task_id)
        
        logger.info(f"提交批量片头片尾任务成功: {len(task_ids)} 个任务")
        
        return BatchTaskResponse(
            success=True,
            task_ids=task_ids,
            message=f"批量任务提交成功，共 {len(task_ids)} 个任务正在后台处理",
            total_tasks=len(task_ids)
        )
        
    except Exception as e:
        logger.error(f"提交批量片头片尾任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交批量任务失败: {str(e)}")

@router.get("/task/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        processor = await get_video_processor()
        task_status = await processor.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")
        
        return TaskStatusResponse(**task_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    try:
        processor = await get_video_processor()
        success = await processor.cancel_task(task_id)
        
        if success:
            return {"success": True, "message": f"任务已取消: {task_id}"}
        else:
            return {"success": False, "message": f"无法取消任务: {task_id}"}
        
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.get("/tasks")
async def get_all_tasks():
    """获取所有任务状态"""
    try:
        processor = await get_video_processor()
        tasks = await processor.get_all_tasks()
        
        return {
            "success": True,
            "tasks": tasks,
            "total": len(tasks)
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.get("/tasks/status/{status}")
async def get_tasks_by_status(status: str):
    """根据状态获取任务"""
    try:
        processor = await get_video_processor()
        all_tasks = await processor.get_all_tasks()
        
        filtered_tasks = [task for task in all_tasks if task['status'] == status]
        
        return {
            "success": True,
            "tasks": filtered_tasks,
            "total": len(filtered_tasks),
            "status": status
        }
        
    except Exception as e:
        logger.error(f"根据状态获取任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}")
