# 异步视频处理系统使用指南

## 概述

异步视频处理系统解决了原有片头片尾处理阻塞主进程的问题，实现了真正的后台处理，支持进度监控、任务取消和状态查询。

## 主要特性

### ✅ 已实现功能

1. **真正的异步处理**
   - 视频处理任务在后台运行，不阻塞主进程
   - 支持多个任务并发执行
   - 可配置最大并发数量

2. **实时进度监控**
   - 解析ffmpeg输出获取处理进度
   - 支持进度百分比、当前时间、总时长等信息
   - 提供进度回调机制

3. **任务管理**
   - 任务状态跟踪（等待中、运行中、已完成、失败、已取消）
   - 支持任务取消操作
   - 任务历史记录和错误信息

4. **完整的API接口**
   - RESTful API支持任务提交、查询、取消
   - 批量处理支持
   - 向后兼容原有同步接口

## API接口

### 1. 提交单个片头片尾任务

```http
POST /api/v1/async-video/intro-outro
Content-Type: application/json

{
    "video_path": "/path/to/video.mp4",
    "output_path": "/path/to/output.mp4",
    "intro_path": "/path/to/intro.mp4",
    "outro_path": "/path/to/outro.mp4",
    "transition_effect": "fade",
    "transition_duration": 1.0,
    "output_quality": "medium"
}
```

**响应：**
```json
{
    "success": true,
    "task_id": "uuid-task-id",
    "message": "任务提交成功，正在后台处理"
}
```

### 2. 提交批量片头片尾任务

```http
POST /api/v1/async-video/batch-intro-outro
Content-Type: application/json

{
    "folder_path": "/path/to/videos",
    "selected_files": ["video1.mp4", "video2.mp4"],
    "intro_path": "/path/to/intro.mp4",
    "outro_path": "/path/to/outro.mp4",
    "output_folder": "/path/to/output",
    "transition_effect": "fade",
    "transition_duration": 1.0,
    "output_quality": "medium",
    "max_concurrent": 3,
    "overwrite_original": false,
    "output_suffix": "_with_intro_outro"
}
```

**响应：**
```json
{
    "success": true,
    "task_ids": ["task-id-1", "task-id-2"],
    "message": "批量任务提交成功，共 2 个任务正在后台处理",
    "total_tasks": 2
}
```

### 3. 查询任务状态

```http
GET /api/v1/async-video/task/{task_id}
```

**响应：**
```json
{
    "task_id": "uuid-task-id",
    "task_type": "intro_outro",
    "status": "running",
    "input_files": ["/path/to/video.mp4", "/path/to/intro.mp4"],
    "output_file": "/path/to/output.mp4",
    "created_at": "2024-01-01T12:00:00",
    "started_at": "2024-01-01T12:00:05",
    "completed_at": null,
    "progress": {
        "current_frame": 1500,
        "total_frames": 3000,
        "current_time": 50.0,
        "total_time": 100.0,
        "percentage": 50.0,
        "fps": 30.0,
        "speed": 1.2,
        "eta_seconds": 42
    },
    "error_message": null
}
```

### 4. 取消任务

```http
DELETE /api/v1/async-video/task/{task_id}
```

**响应：**
```json
{
    "success": true,
    "message": "任务已取消: task-id"
}
```

### 5. 获取所有任务

```http
GET /api/v1/async-video/tasks
```

**响应：**
```json
{
    "success": true,
    "tasks": [...],
    "total": 10
}
```

### 6. 根据状态获取任务

```http
GET /api/v1/async-video/tasks/status/running
```

**响应：**
```json
{
    "success": true,
    "tasks": [...],
    "total": 3,
    "status": "running"
}
```

## 任务状态说明

- **pending**: 等待中 - 任务已提交，等待处理
- **running**: 运行中 - 任务正在执行
- **completed**: 已完成 - 任务成功完成
- **failed**: 失败 - 任务执行失败
- **cancelled**: 已取消 - 任务被用户取消

## 使用示例

### Python客户端示例

```python
import asyncio
import aiohttp

async def submit_and_monitor_task():
    async with aiohttp.ClientSession() as session:
        # 提交任务
        async with session.post('http://localhost:8000/api/v1/async-video/intro-outro', json={
            "video_path": "/path/to/video.mp4",
            "output_path": "/path/to/output.mp4",
            "intro_path": "/path/to/intro.mp4",
            "output_quality": "high"
        }) as resp:
            result = await resp.json()
            task_id = result['task_id']
            print(f"任务已提交: {task_id}")
        
        # 监控进度
        while True:
            async with session.get(f'http://localhost:8000/api/v1/async-video/task/{task_id}') as resp:
                status = await resp.json()
                print(f"进度: {status['progress']['percentage']:.1f}%")
                
                if status['status'] in ['completed', 'failed', 'cancelled']:
                    print(f"任务完成，状态: {status['status']}")
                    break
            
            await asyncio.sleep(2)

# 运行示例
asyncio.run(submit_and_monitor_task())
```

### JavaScript客户端示例

```javascript
async function submitAndMonitorTask() {
    // 提交任务
    const response = await fetch('/api/v1/async-video/intro-outro', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            video_path: '/path/to/video.mp4',
            output_path: '/path/to/output.mp4',
            intro_path: '/path/to/intro.mp4',
            output_quality: 'medium'
        })
    });
    
    const result = await response.json();
    const taskId = result.task_id;
    console.log(`任务已提交: ${taskId}`);
    
    // 监控进度
    const checkProgress = async () => {
        const statusResponse = await fetch(`/api/v1/async-video/task/${taskId}`);
        const status = await statusResponse.json();
        
        console.log(`进度: ${status.progress.percentage.toFixed(1)}%`);
        
        if (['completed', 'failed', 'cancelled'].includes(status.status)) {
            console.log(`任务完成，状态: ${status.status}`);
            return;
        }
        
        setTimeout(checkProgress, 2000);
    };
    
    checkProgress();
}
```

## 配置说明

### 处理器配置

```python
# 在 core/src/services/async_video_processor.py 中
processor = AsyncVideoProcessor(
    max_concurrent_tasks=3  # 最大并发任务数
)
```

### 输出质量设置

- **high**: CRF 18，高质量输出
- **medium**: CRF 23，中等质量输出（默认）
- **low**: CRF 28，低质量输出

## 性能优化建议

1. **合理设置并发数**
   - 根据系统资源调整 `max_concurrent_tasks`
   - 建议不超过CPU核心数

2. **监控系统资源**
   - 处理大文件时注意内存使用
   - 确保有足够的磁盘空间

3. **批量处理优化**
   - 对于大量文件，使用批量接口
   - 合理设置输出文件夹避免冲突

## 故障排除

### 常见问题

1. **任务一直处于pending状态**
   - 检查处理器是否正常启动
   - 确认没有超过最大并发限制

2. **任务失败**
   - 检查输入文件是否存在
   - 确认ffmpeg是否正确安装
   - 查看错误信息获取详细原因

3. **进度不更新**
   - 确认ffmpeg版本支持progress输出
   - 检查日志中的错误信息

### 日志查看

```bash
# 查看处理器日志
tail -f logs/video_processor.log

# 查看API日志
tail -f logs/api.log
```

## 向后兼容性

原有的同步接口 `/api/v1/filesystem/add-intro-outro` 仍然可用，但建议迁移到新的异步接口以获得更好的用户体验。

## 未来扩展

系统设计支持扩展其他视频处理功能：
- 视频合并
- 视频裁剪
- 视频加速
- 音频分离

只需在 `VideoTaskType` 中添加新类型并实现相应的处理逻辑即可。
