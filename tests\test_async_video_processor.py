"""
异步视频处理器测试
"""

import asyncio
import os
import tempfile
import pytest
import sys
from unittest.mock import Mock, patch, AsyncMock

# 添加core模块路径
core_src_path = os.path.join(os.path.dirname(__file__), '../core/src')
if core_src_path not in sys.path:
    sys.path.insert(0, core_src_path)

try:
    from services.async_video_processor import (
        AsyncVideoProcessor, VideoTask, VideoTaskStatus, VideoTaskType,
        get_video_processor
    )
except ImportError as e:
    pytest.skip(f"无法导入异步视频处理器: {e}", allow_module_level=True)

@pytest.fixture
async def video_processor():
    """创建视频处理器实例"""
    processor = AsyncVideoProcessor(max_concurrent_tasks=2)
    await processor.start()
    yield processor
    await processor.stop()

@pytest.fixture
def temp_video_files():
    """创建临时视频文件"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建模拟视频文件
        video_path = os.path.join(temp_dir, "test_video.mp4")
        intro_path = os.path.join(temp_dir, "intro.mp4")
        outro_path = os.path.join(temp_dir, "outro.mp4")
        output_path = os.path.join(temp_dir, "output.mp4")
        
        # 创建空文件模拟视频
        for path in [video_path, intro_path, outro_path]:
            with open(path, 'w') as f:
                f.write("fake video content")
        
        yield {
            'video_path': video_path,
            'intro_path': intro_path,
            'outro_path': outro_path,
            'output_path': output_path,
            'temp_dir': temp_dir
        }

class TestAsyncVideoProcessor:
    """异步视频处理器测试类"""
    
    @pytest.mark.asyncio
    async def test_processor_start_stop(self):
        """测试处理器启动和停止"""
        processor = AsyncVideoProcessor(max_concurrent_tasks=1)
        
        # 测试启动
        await processor.start()
        assert processor.is_running is True
        assert len(processor.workers) == 1
        
        # 测试停止
        await processor.stop()
        assert processor.is_running is False
        assert len(processor.workers) == 0
    
    @pytest.mark.asyncio
    async def test_submit_intro_outro_task(self, video_processor, temp_video_files):
        """测试提交片头片尾任务"""
        files = temp_video_files
        
        # 模拟视频信息获取
        with patch.object(video_processor, '_get_video_info') as mock_get_info:
            mock_get_info.return_value = {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'duration': 60.0
            }
            
            # 提交任务
            task_id = await video_processor.submit_intro_outro_task(
                video_path=files['video_path'],
                output_path=files['output_path'],
                intro_path=files['intro_path'],
                outro_path=files['outro_path']
            )
            
            # 验证任务创建
            assert task_id is not None
            assert task_id in video_processor.tasks
            
            task = video_processor.tasks[task_id]
            assert task.task_type == VideoTaskType.INTRO_OUTRO
            assert task.status == VideoTaskStatus.PENDING
            assert task.input_files == [files['video_path'], files['intro_path'], files['outro_path']]
            assert task.output_file == files['output_path']
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, video_processor, temp_video_files):
        """测试获取任务状态"""
        files = temp_video_files
        
        with patch.object(video_processor, '_get_video_info') as mock_get_info:
            mock_get_info.return_value = {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'duration': 60.0
            }
            
            # 提交任务
            task_id = await video_processor.submit_intro_outro_task(
                video_path=files['video_path'],
                output_path=files['output_path'],
                intro_path=files['intro_path']
            )
            
            # 获取任务状态
            status = await video_processor.get_task_status(task_id)
            
            assert status is not None
            assert status['task_id'] == task_id
            assert status['task_type'] == VideoTaskType.INTRO_OUTRO.value
            assert status['status'] == VideoTaskStatus.PENDING.value
            assert status['input_files'] == [files['video_path'], files['intro_path']]
            assert status['output_file'] == files['output_path']
            assert 'progress' in status
            assert 'created_at' in status
    
    @pytest.mark.asyncio
    async def test_cancel_pending_task(self, video_processor, temp_video_files):
        """测试取消等待中的任务"""
        files = temp_video_files
        
        with patch.object(video_processor, '_get_video_info') as mock_get_info:
            mock_get_info.return_value = {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'duration': 60.0
            }
            
            # 提交任务
            task_id = await video_processor.submit_intro_outro_task(
                video_path=files['video_path'],
                output_path=files['output_path'],
                intro_path=files['intro_path']
            )
            
            # 取消任务
            success = await video_processor.cancel_task(task_id)
            
            assert success is True
            
            # 验证任务状态
            task = video_processor.tasks[task_id]
            assert task.status == VideoTaskStatus.CANCELLED
            assert task.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_get_all_tasks(self, video_processor, temp_video_files):
        """测试获取所有任务"""
        files = temp_video_files
        
        with patch.object(video_processor, '_get_video_info') as mock_get_info:
            mock_get_info.return_value = {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'duration': 60.0
            }
            
            # 提交多个任务
            task_ids = []
            for i in range(3):
                output_path = os.path.join(files['temp_dir'], f"output_{i}.mp4")
                task_id = await video_processor.submit_intro_outro_task(
                    video_path=files['video_path'],
                    output_path=output_path,
                    intro_path=files['intro_path']
                )
                task_ids.append(task_id)
            
            # 获取所有任务
            all_tasks = await video_processor.get_all_tasks()
            
            assert len(all_tasks) == 3
            for task in all_tasks:
                assert task['task_id'] in task_ids
                assert task['task_type'] == VideoTaskType.INTRO_OUTRO.value
                assert task['status'] == VideoTaskStatus.PENDING.value
    
    @pytest.mark.asyncio
    async def test_file_not_found_error(self, video_processor):
        """测试文件不存在错误"""
        with pytest.raises(FileNotFoundError):
            await video_processor.submit_intro_outro_task(
                video_path="/nonexistent/video.mp4",
                output_path="/tmp/output.mp4",
                intro_path="/nonexistent/intro.mp4"
            )
    
    @pytest.mark.asyncio
    async def test_get_video_processor_singleton(self):
        """测试获取视频处理器单例"""
        # 清除全局实例
        import services.async_video_processor as module
        module._video_processor = None
        
        # 获取处理器实例
        processor1 = await get_video_processor()
        processor2 = await get_video_processor()
        
        # 验证是同一个实例
        assert processor1 is processor2
        assert processor1.is_running is True
        
        # 清理
        await processor1.stop()
        module._video_processor = None

class TestVideoTaskProgress:
    """视频任务进度测试"""
    
    def test_progress_initialization(self):
        """测试进度初始化"""
        from services.async_video_processor import VideoTaskProgress
        
        progress = VideoTaskProgress()
        assert progress.current_frame == 0
        assert progress.total_frames == 0
        assert progress.current_time == 0.0
        assert progress.total_time == 0.0
        assert progress.percentage == 0.0
        assert progress.fps == 0.0
        assert progress.speed == 0.0
        assert progress.eta_seconds == 0

class TestVideoTask:
    """视频任务测试"""
    
    def test_task_creation(self):
        """测试任务创建"""
        from datetime import datetime
        
        task = VideoTask(
            task_id="test-task-id",
            task_type=VideoTaskType.INTRO_OUTRO,
            status=VideoTaskStatus.PENDING,
            input_files=["/path/to/video.mp4"],
            output_file="/path/to/output.mp4",
            parameters={"quality": "high"},
            created_at=datetime.now()
        )
        
        assert task.task_id == "test-task-id"
        assert task.task_type == VideoTaskType.INTRO_OUTRO
        assert task.status == VideoTaskStatus.PENDING
        assert task.input_files == ["/path/to/video.mp4"]
        assert task.output_file == "/path/to/output.mp4"
        assert task.parameters == {"quality": "high"}
        assert task.progress is not None
        assert task.started_at is None
        assert task.completed_at is None
        assert task.error_message is None
        assert task.process is None

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
