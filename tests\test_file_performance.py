"""
文件操作性能测试
验证异步文件操作的性能改进和并发控制
"""

import asyncio
import os
import tempfile
import time
import shutil
import pytest
import logging
from pathlib import Path
from typing import List
import sys

# 添加core模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../core/src'))

from services.common.async_file_utils import get_async_file_utils
from services.common.resource_limiter import get_resource_limiter, initialize_resource_limiter
from services.file_operation_queue import get_file_operation_queue, FileOperationType

logger = logging.getLogger(__name__)

class TestFilePerformance:
    """文件操作性能测试"""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """设置和清理"""
        # 初始化资源限制器
        await initialize_resource_limiter()
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.source_dir = os.path.join(self.temp_dir, "source")
        self.target_dir = os.path.join(self.temp_dir, "target")
        os.makedirs(self.source_dir, exist_ok=True)
        os.makedirs(self.target_dir, exist_ok=True)
        
        yield
        
        # 清理
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def create_test_files(self, count: int, size_mb: int = 1) -> List[str]:
        """创建测试文件"""
        files = []
        for i in range(count):
            file_path = os.path.join(self.source_dir, f"test_file_{i}.dat")
            
            # 创建指定大小的文件
            with open(file_path, 'wb') as f:
                data = b'0' * (1024 * 1024 * size_mb)  # size_mb MB
                f.write(data)
            
            files.append(file_path)
        
        return files
    
    async def test_sync_vs_async_copy_performance(self):
        """测试同步vs异步复制性能"""
        # 创建测试文件
        test_files = self.create_test_files(10, 5)  # 10个5MB文件
        
        # 同步复制测试
        sync_start = time.time()
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(self.target_dir, f"sync_copy_{i}.dat")
            shutil.copy2(source_file, target_file)
        sync_time = time.time() - sync_start
        
        # 清理目标文件
        for file in os.listdir(self.target_dir):
            os.remove(os.path.join(self.target_dir, file))
        
        # 异步复制测试
        async_file_utils = get_async_file_utils()
        async_start = time.time()
        
        tasks = []
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(self.target_dir, f"async_copy_{i}.dat")
            task = async_file_utils.copy_file_async(source_file, target_file)
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        async_time = time.time() - async_start
        
        logger.info(f"同步复制时间: {sync_time:.2f}秒")
        logger.info(f"异步复制时间: {async_time:.2f}秒")
        logger.info(f"性能提升: {((sync_time - async_time) / sync_time * 100):.1f}%")
        
        # 异步应该更快（在有足够并发的情况下）
        assert async_time < sync_time * 1.2  # 允许20%的误差
    
    async def test_batch_operations_performance(self):
        """测试批量操作性能"""
        # 创建测试文件
        test_files = self.create_test_files(20, 2)  # 20个2MB文件
        
        async_file_utils = get_async_file_utils()
        
        # 准备批量操作
        operations = []
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(self.target_dir, f"batch_copy_{i}.dat")
            operations.append({
                "source": source_file,
                "target": target_file
            })
        
        # 批量复制测试
        def progress_callback(completed, total):
            logger.info(f"批量复制进度: {completed}/{total}")
        
        start_time = time.time()
        result = await async_file_utils.batch_copy_files(
            operations, 
            max_concurrent=5,
            progress_callback=progress_callback
        )
        batch_time = time.time() - start_time
        
        logger.info(f"批量复制时间: {batch_time:.2f}秒")
        logger.info(f"成功复制: {result['success']}/{len(operations)}")
        logger.info(f"错误: {len(result['errors'])}")
        
        # 验证结果
        assert result["success"] == len(operations)
        assert len(result["errors"]) == 0
    
    async def test_resource_limiter_effectiveness(self):
        """测试资源限制器效果"""
        resource_limiter = get_resource_limiter()
        
        # 获取初始资源状态
        initial_status = await resource_limiter.get_resource_status()
        logger.info(f"初始资源状态: {initial_status}")
        
        # 创建大量文件操作任务
        test_files = self.create_test_files(50, 1)  # 50个1MB文件
        async_file_utils = get_async_file_utils()
        
        # 启动大量并发操作
        tasks = []
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(self.target_dir, f"limited_copy_{i}.dat")
            task = async_file_utils.copy_file_async(source_file, target_file)
            tasks.append(task)
        
        start_time = time.time()
        await asyncio.gather(*tasks)
        end_time = time.time()
        
        # 获取最终资源状态
        final_status = await resource_limiter.get_resource_status()
        logger.info(f"最终资源状态: {final_status}")
        
        logger.info(f"受限并发操作时间: {end_time - start_time:.2f}秒")
        
        # 验证资源限制器工作正常
        assert final_status.active_file_operations == 0  # 所有操作应该完成
    
    async def test_file_operation_queue(self):
        """测试文件操作队列"""
        queue = await get_file_operation_queue()
        
        # 创建测试文件
        test_files = self.create_test_files(5, 1)
        
        # 提交复制操作到队列
        operation_ids = []
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(self.target_dir, f"queue_copy_{i}.dat")
            
            operation_id = await queue.submit_operation(
                FileOperationType.COPY,
                {
                    "source": source_file,
                    "target": target_file
                }
            )
            operation_ids.append(operation_id)
        
        # 等待所有操作完成
        max_wait = 30  # 最大等待30秒
        start_wait = time.time()
        
        while time.time() - start_wait < max_wait:
            all_completed = True
            for operation_id in operation_ids:
                operation = await queue.get_operation_status(operation_id)
                if operation and operation.status.value not in ["completed", "failed"]:
                    all_completed = False
                    break
            
            if all_completed:
                break
            
            await asyncio.sleep(0.5)
        
        # 验证所有操作完成
        for operation_id in operation_ids:
            operation = await queue.get_operation_status(operation_id)
            assert operation is not None
            assert operation.status.value == "completed"
            assert operation.result["success"] is True
        
        logger.info("文件操作队列测试完成")
    
    async def test_md5_calculation_performance(self):
        """测试MD5计算性能"""
        # 创建大文件
        large_file = os.path.join(self.source_dir, "large_file.dat")
        with open(large_file, 'wb') as f:
            data = b'0' * (1024 * 1024 * 50)  # 50MB文件
            f.write(data)
        
        async_file_utils = get_async_file_utils()
        
        # 异步MD5计算
        start_time = time.time()
        md5_hash = await async_file_utils.calculate_md5_async(large_file)
        async_time = time.time() - start_time
        
        logger.info(f"异步MD5计算时间: {async_time:.2f}秒")
        logger.info(f"MD5哈希: {md5_hash}")
        
        # 验证MD5计算成功
        assert md5_hash is not None
        assert len(md5_hash) == 32  # MD5哈希长度为32字符
    
    async def test_concurrent_operations_stability(self):
        """测试并发操作稳定性"""
        # 创建测试文件
        test_files = self.create_test_files(30, 2)
        async_file_utils = get_async_file_utils()
        
        # 混合操作：复制、移动、删除
        tasks = []
        
        # 复制操作
        for i in range(0, 10):
            source_file = test_files[i]
            target_file = os.path.join(self.target_dir, f"concurrent_copy_{i}.dat")
            task = async_file_utils.copy_file_async(source_file, target_file)
            tasks.append(task)
        
        # MD5计算操作
        for i in range(10, 20):
            source_file = test_files[i]
            task = async_file_utils.calculate_md5_async(source_file)
            tasks.append(task)
        
        # 执行所有操作
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 检查结果
        success_count = 0
        error_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                error_count += 1
                logger.error(f"操作异常: {result}")
            else:
                success_count += 1
        
        logger.info(f"并发操作完成时间: {end_time - start_time:.2f}秒")
        logger.info(f"成功操作: {success_count}, 失败操作: {error_count}")
        
        # 大部分操作应该成功
        assert success_count >= len(tasks) * 0.8  # 至少80%成功率

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
