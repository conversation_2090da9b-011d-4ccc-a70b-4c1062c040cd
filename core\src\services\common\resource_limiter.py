"""
资源限制器
用于控制文件操作的并发数量和资源使用，防止系统过载
"""

import asyncio
import psutil
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading

logger = logging.getLogger(__name__)

@dataclass
class ResourceLimits:
    """资源限制配置"""
    max_concurrent_file_ops: int = 10  # 最大并发文件操作数
    max_memory_usage_percent: float = 80.0  # 最大内存使用百分比
    max_disk_io_mbps: float = 100.0  # 最大磁盘IO速度(MB/s)
    max_cpu_usage_percent: float = 80.0  # 最大CPU使用百分比
    check_interval: float = 1.0  # 资源检查间隔(秒)

@dataclass
class ResourceStatus:
    """资源状态"""
    memory_usage_percent: float
    cpu_usage_percent: float
    disk_io_mbps: float
    active_file_operations: int
    is_overloaded: bool

class ResourceLimiter:
    """资源限制器"""
    
    def __init__(self, limits: Optional[ResourceLimits] = None):
        self.limits = limits or ResourceLimits()
        self.semaphore = asyncio.Semaphore(self.limits.max_concurrent_file_ops)
        self.active_operations = 0
        self.last_disk_io_check = datetime.now()
        self.last_disk_io_bytes = 0
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self._lock = threading.Lock()
        
    async def start_monitoring(self):
        """开始资源监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_resources())
        logger.info("资源监控已启动")
    
    async def stop_monitoring(self):
        """停止资源监控"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("资源监控已停止")
    
    async def acquire_file_operation_permit(self, operation_type: str = "file_op") -> bool:
        """获取文件操作许可"""
        try:
            # 检查资源状态
            status = await self.get_resource_status()
            if status.is_overloaded:
                logger.warning(f"系统资源过载，拒绝文件操作: {operation_type}")
                return False
            
            # 获取信号量
            await self.semaphore.acquire()
            
            with self._lock:
                self.active_operations += 1
            
            logger.debug(f"获取文件操作许可: {operation_type} (活跃操作: {self.active_operations})")
            return True
            
        except Exception as e:
            logger.error(f"获取文件操作许可失败: {str(e)}")
            return False
    
    def release_file_operation_permit(self, operation_type: str = "file_op"):
        """释放文件操作许可"""
        try:
            self.semaphore.release()
            
            with self._lock:
                self.active_operations = max(0, self.active_operations - 1)
            
            logger.debug(f"释放文件操作许可: {operation_type} (活跃操作: {self.active_operations})")
            
        except Exception as e:
            logger.error(f"释放文件操作许可失败: {str(e)}")
    
    async def get_resource_status(self) -> ResourceStatus:
        """获取当前资源状态"""
        try:
            # 获取内存使用率
            memory = psutil.virtual_memory()
            memory_usage_percent = memory.percent
            
            # 获取CPU使用率
            cpu_usage_percent = psutil.cpu_percent(interval=0.1)
            
            # 获取磁盘IO速度
            disk_io_mbps = await self._get_disk_io_speed()
            
            # 判断是否过载
            is_overloaded = (
                memory_usage_percent > self.limits.max_memory_usage_percent or
                cpu_usage_percent > self.limits.max_cpu_usage_percent or
                disk_io_mbps > self.limits.max_disk_io_mbps or
                self.active_operations >= self.limits.max_concurrent_file_ops
            )
            
            return ResourceStatus(
                memory_usage_percent=memory_usage_percent,
                cpu_usage_percent=cpu_usage_percent,
                disk_io_mbps=disk_io_mbps,
                active_file_operations=self.active_operations,
                is_overloaded=is_overloaded
            )
            
        except Exception as e:
            logger.error(f"获取资源状态失败: {str(e)}")
            # 返回安全的默认状态
            return ResourceStatus(
                memory_usage_percent=0.0,
                cpu_usage_percent=0.0,
                disk_io_mbps=0.0,
                active_file_operations=self.active_operations,
                is_overloaded=False
            )
    
    async def _get_disk_io_speed(self) -> float:
        """获取磁盘IO速度(MB/s)"""
        try:
            current_time = datetime.now()
            disk_io = psutil.disk_io_counters()
            
            if disk_io is None:
                return 0.0
            
            current_bytes = disk_io.read_bytes + disk_io.write_bytes
            
            # 计算速度
            if self.last_disk_io_bytes > 0:
                time_diff = (current_time - self.last_disk_io_check).total_seconds()
                if time_diff > 0:
                    bytes_diff = current_bytes - self.last_disk_io_bytes
                    mbps = (bytes_diff / (1024 * 1024)) / time_diff
                else:
                    mbps = 0.0
            else:
                mbps = 0.0
            
            # 更新记录
            self.last_disk_io_check = current_time
            self.last_disk_io_bytes = current_bytes
            
            return max(0.0, mbps)
            
        except Exception as e:
            logger.error(f"获取磁盘IO速度失败: {str(e)}")
            return 0.0
    
    async def _monitor_resources(self):
        """资源监控循环"""
        while self.is_monitoring:
            try:
                status = await self.get_resource_status()
                
                # 记录资源状态
                if status.is_overloaded:
                    logger.warning(
                        f"系统资源过载 - "
                        f"内存: {status.memory_usage_percent:.1f}%, "
                        f"CPU: {status.cpu_usage_percent:.1f}%, "
                        f"磁盘IO: {status.disk_io_mbps:.1f}MB/s, "
                        f"活跃操作: {status.active_file_operations}"
                    )
                else:
                    logger.debug(
                        f"系统资源正常 - "
                        f"内存: {status.memory_usage_percent:.1f}%, "
                        f"CPU: {status.cpu_usage_percent:.1f}%, "
                        f"磁盘IO: {status.disk_io_mbps:.1f}MB/s, "
                        f"活跃操作: {status.active_file_operations}"
                    )
                
                await asyncio.sleep(self.limits.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"资源监控异常: {str(e)}")
                await asyncio.sleep(self.limits.check_interval)
    
    async def wait_for_resources(self, timeout: float = 30.0) -> bool:
        """等待资源可用"""
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < timeout:
            status = await self.get_resource_status()
            if not status.is_overloaded:
                return True
            
            logger.info("等待系统资源可用...")
            await asyncio.sleep(1.0)
        
        logger.warning(f"等待资源超时: {timeout}秒")
        return False
    
    def update_limits(self, new_limits: ResourceLimits):
        """更新资源限制"""
        old_max_ops = self.limits.max_concurrent_file_ops
        self.limits = new_limits
        
        # 如果并发限制改变，更新信号量
        if new_limits.max_concurrent_file_ops != old_max_ops:
            # 创建新的信号量
            self.semaphore = asyncio.Semaphore(new_limits.max_concurrent_file_ops)
            logger.info(f"更新并发限制: {old_max_ops} -> {new_limits.max_concurrent_file_ops}")

class FileOperationContext:
    """文件操作上下文管理器"""
    
    def __init__(self, limiter: ResourceLimiter, operation_type: str = "file_op"):
        self.limiter = limiter
        self.operation_type = operation_type
        self.permit_acquired = False
    
    async def __aenter__(self):
        """进入上下文"""
        self.permit_acquired = await self.limiter.acquire_file_operation_permit(self.operation_type)
        if not self.permit_acquired:
            raise RuntimeError(f"无法获取文件操作许可: {self.operation_type}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.permit_acquired:
            self.limiter.release_file_operation_permit(self.operation_type)

# 全局实例
_resource_limiter = None

def get_resource_limiter() -> ResourceLimiter:
    """获取资源限制器实例"""
    global _resource_limiter
    if _resource_limiter is None:
        _resource_limiter = ResourceLimiter()
    return _resource_limiter

async def initialize_resource_limiter():
    """初始化资源限制器"""
    limiter = get_resource_limiter()
    await limiter.start_monitoring()
    logger.info("资源限制器初始化完成")
