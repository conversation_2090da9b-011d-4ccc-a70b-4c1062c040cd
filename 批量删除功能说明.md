# 文件管理界面批量删除功能

## 功能概述

在文件管理界面添加了批量删除功能，用户可以选择多个文件或文件夹，然后通过批量删除按钮一次性删除所有选中的项目。

## 功能特性

### 1. 批量删除按钮
- 位置：工具栏右侧，音频处理按钮之后
- 样式：红色危险按钮，带删除图标
- 状态：当没有选中项目时按钮禁用
- 显示：按钮文本显示选中项目数量

### 2. 确认对话框
- **警告信息**：明确提示用户即将删除的项目数量和操作不可恢复
- **项目列表**：显示所有将要删除的文件和文件夹
  - 文件夹显示蓝色文件夹图标
  - 视频文件显示绿色播放图标
  - 其他文件显示灰色文档图标
  - 显示文件大小（仅文件）
- **已发布文件警告**：
  - 特殊标记已在社交媒体平台发布的文件
  - 显示发布平台数量
  - 红色警告提示框提醒用户注意

### 3. 安全机制
- **二次确认**：通过对话框确认删除操作
- **已发布文件提醒**：特别标记和警告已发布的文件
- **错误处理**：显示删除过程中的错误信息
- **状态反馈**：删除过程中显示加载状态

## 技术实现

### 前端组件
- **按钮组件**：`el-button` 带 `Delete` 图标
- **对话框组件**：`el-dialog` 用于确认删除
- **警告组件**：`el-alert` 显示警告信息
- **列表组件**：`el-card` 显示选中项目列表

### 状态管理
```javascript
// 对话框显示状态
const showBatchDeleteDialog = ref(false)
// 删除加载状态
const batchDeleteLoading = ref(false)
// 已发布文件数量计算
const publishedItemsCount = computed(() => {
  return selectedItems.value.filter(item => 
    !item.is_directory && getFilePublishedPlatformCount(item) > 0
  ).length
})
```

### 核心方法
```javascript
// 打开批量删除对话框
const openBatchDeleteDialog = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件或文件夹')
    return
  }
  showBatchDeleteDialog.value = true
}

// 执行批量删除
const executeBatchDelete = async () => {
  // 构建文件路径列表
  const filePaths = selectedItems.value.map(item => item.path)
  
  // 调用批量删除API
  const response = await batchDeleteFiles(filePaths)
  
  // 处理响应和刷新界面
}
```

### API 接口
- **接口地址**：`/api/v1/filesystem/md5-records/batch-delete`
- **请求方法**：POST
- **请求参数**：`{ file_paths: string[] }`
- **响应格式**：`{ success: boolean, deleted_count: number, errors: string[] }`

## 用户体验

### 操作流程
1. 用户在文件列表中选择要删除的文件或文件夹
2. 点击工具栏中的"批量删除"按钮
3. 系统弹出确认对话框，显示将要删除的项目列表
4. 如果有已发布文件，系统会特别标记和警告
5. 用户确认后执行删除操作
6. 显示删除结果并刷新文件列表

### 视觉设计
- **按钮设计**：红色危险按钮，清晰表达删除操作的危险性
- **对话框设计**：宽度700px，内容布局清晰
- **警告设计**：使用橙色和红色警告框，突出重要信息
- **列表设计**：已发布文件使用红色背景和左边框突出显示

### 响应式设计
- 项目列表最大高度300px，超出时显示滚动条
- 已发布文件特殊样式标记
- 悬停效果增强交互体验

## 安全考虑

1. **物理删除**：文件被物理删除，但MD5记录会保留
2. **已发布文件保护**：特别标记已发布文件，提醒用户谨慎操作
3. **错误处理**：完整的错误处理和用户反馈
4. **操作确认**：必须通过确认对话框才能执行删除
5. **状态清理**：删除完成后自动清空选中项目和刷新列表

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加更多的文件类型图标
- 可以扩展更多的安全检查机制
- 可以添加更多的批量操作功能
- 样式系统支持主题定制
