#!/usr/bin/env python3
"""
简单的文件操作优化验证脚本
"""

import asyncio
import os
import tempfile
import time
import shutil
import sys
from pathlib import Path

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

async def test_async_file_operations():
    """测试异步文件操作"""
    print("🚀 开始测试异步文件操作优化...")
    
    # 导入异步文件工具
    try:
        from app.api.v1.filesystem import get_async_file_utils
        async_file_utils = get_async_file_utils()
        print("✅ 成功导入异步文件工具")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 创建临时目录和测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        source_dir = os.path.join(temp_dir, "source")
        target_dir = os.path.join(temp_dir, "target")
        os.makedirs(source_dir, exist_ok=True)
        os.makedirs(target_dir, exist_ok=True)
        
        # 创建测试文件
        test_files = []
        for i in range(5):
            file_path = os.path.join(source_dir, f"test_file_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(f"测试文件内容 {i}\n" * 1000)  # 创建一些内容
            test_files.append(file_path)
        
        print(f"📁 创建了 {len(test_files)} 个测试文件")
        
        # 测试1: 异步复制单个文件
        print("\n🔄 测试1: 异步复制单个文件")
        source_file = test_files[0]
        target_file = os.path.join(target_dir, "copied_file.txt")
        
        start_time = time.time()
        success = await async_file_utils.copy_file_async(source_file, target_file)
        copy_time = time.time() - start_time
        
        if success and os.path.exists(target_file):
            print(f"✅ 单文件复制成功，耗时: {copy_time:.3f}秒")
        else:
            print("❌ 单文件复制失败")
            return False
        
        # 测试2: 异步计算MD5
        print("\n🔍 测试2: 异步计算MD5")
        start_time = time.time()
        md5_hash = await async_file_utils.calculate_md5_async(source_file)
        md5_time = time.time() - start_time
        
        if md5_hash and len(md5_hash) == 32:
            print(f"✅ MD5计算成功: {md5_hash[:8]}..., 耗时: {md5_time:.3f}秒")
        else:
            print("❌ MD5计算失败")
            return False
        
        # 测试3: 批量复制文件
        print("\n📦 测试3: 批量复制文件")
        operations = []
        for i, source_file in enumerate(test_files[1:]):
            target_file = os.path.join(target_dir, f"batch_copy_{i}.txt")
            operations.append({
                "source": source_file,
                "target": target_file
            })
        
        def progress_callback(completed, total):
            print(f"  进度: {completed}/{total} ({completed/total*100:.1f}%)")
        
        start_time = time.time()
        result = await async_file_utils.batch_copy_files(
            operations,
            max_concurrent=3,
            progress_callback=progress_callback
        )
        batch_time = time.time() - start_time
        
        if result["success"] == len(operations) and len(result["errors"]) == 0:
            print(f"✅ 批量复制成功: {result['success']}/{len(operations)} 文件，耗时: {batch_time:.3f}秒")
        else:
            print(f"⚠️ 批量复制部分成功: {result['success']}/{len(operations)} 文件")
            if result["errors"]:
                for error in result["errors"]:
                    print(f"  错误: {error}")
        
        # 测试4: 批量删除文件
        print("\n🗑️ 测试4: 批量删除文件")
        files_to_delete = [op["target"] for op in operations]
        
        start_time = time.time()
        result = await async_file_utils.batch_delete_files(
            files_to_delete,
            max_concurrent=3,
            progress_callback=progress_callback
        )
        delete_time = time.time() - start_time
        
        if result["success"] == len(files_to_delete) and len(result["errors"]) == 0:
            print(f"✅ 批量删除成功: {result['success']}/{len(files_to_delete)} 文件，耗时: {delete_time:.3f}秒")
        else:
            print(f"⚠️ 批量删除部分成功: {result['success']}/{len(files_to_delete)} 文件")
        
        # 测试5: 文件移动
        print("\n🚚 测试5: 文件移动")
        source_file = test_files[1]
        target_file = os.path.join(target_dir, "moved_file.txt")
        
        start_time = time.time()
        success = await async_file_utils.move_file_async(source_file, target_file)
        move_time = time.time() - start_time
        
        if success and os.path.exists(target_file) and not os.path.exists(source_file):
            print(f"✅ 文件移动成功，耗时: {move_time:.3f}秒")
        else:
            print("❌ 文件移动失败")
        
        print(f"\n🎉 所有测试完成!")
        print(f"📊 性能总结:")
        print(f"  - 单文件复制: {copy_time:.3f}秒")
        print(f"  - MD5计算: {md5_time:.3f}秒")
        print(f"  - 批量复制({len(operations)}文件): {batch_time:.3f}秒")
        print(f"  - 批量删除({len(files_to_delete)}文件): {delete_time:.3f}秒")
        print(f"  - 文件移动: {move_time:.3f}秒")
        
        return True

def test_sync_vs_async_comparison():
    """比较同步和异步操作的性能"""
    print("\n⚡ 性能对比测试 (同步 vs 异步)")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        source_dir = os.path.join(temp_dir, "source")
        sync_target_dir = os.path.join(temp_dir, "sync_target")
        async_target_dir = os.path.join(temp_dir, "async_target")
        
        os.makedirs(source_dir, exist_ok=True)
        os.makedirs(sync_target_dir, exist_ok=True)
        os.makedirs(async_target_dir, exist_ok=True)
        
        # 创建测试文件
        test_files = []
        for i in range(10):
            file_path = os.path.join(source_dir, f"perf_test_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(f"性能测试文件 {i}\n" * 5000)  # 较大的文件
            test_files.append(file_path)
        
        print(f"📁 创建了 {len(test_files)} 个性能测试文件")
        
        # 同步复制测试
        print("\n🐌 同步复制测试")
        sync_start = time.time()
        for i, source_file in enumerate(test_files):
            target_file = os.path.join(sync_target_dir, f"sync_copy_{i}.txt")
            shutil.copy2(source_file, target_file)
        sync_time = time.time() - sync_start
        print(f"同步复制耗时: {sync_time:.3f}秒")
        
        # 异步复制测试
        async def async_copy_test():
            from app.api.v1.filesystem import get_async_file_utils
            async_file_utils = get_async_file_utils()
            
            operations = []
            for i, source_file in enumerate(test_files):
                target_file = os.path.join(async_target_dir, f"async_copy_{i}.txt")
                operations.append({
                    "source": source_file,
                    "target": target_file
                })
            
            async_start = time.time()
            result = await async_file_utils.batch_copy_files(operations, max_concurrent=5)
            async_time = time.time() - async_start
            
            return async_time, result
        
        print("\n🚀 异步复制测试")
        async_time, result = asyncio.run(async_copy_test())
        print(f"异步复制耗时: {async_time:.3f}秒")
        
        # 性能对比
        if async_time < sync_time:
            improvement = ((sync_time - async_time) / sync_time) * 100
            print(f"🎯 性能提升: {improvement:.1f}% (异步更快)")
        else:
            degradation = ((async_time - sync_time) / sync_time) * 100
            print(f"⚠️ 性能下降: {degradation:.1f}% (可能由于文件较小或系统负载)")
        
        print(f"📈 详细对比:")
        print(f"  - 同步方式: {sync_time:.3f}秒")
        print(f"  - 异步方式: {async_time:.3f}秒")
        print(f"  - 成功复制: {result['success']}/{len(test_files)} 文件")

async def main():
    """主函数"""
    print("🔧 ThunderHub 文件操作优化验证")
    print("=" * 50)
    
    try:
        # 测试异步文件操作
        success = await test_async_file_operations()
        
        if success:
            print("\n" + "=" * 50)
            # 性能对比测试
            test_sync_vs_async_comparison()
            
            print("\n" + "=" * 50)
            print("✅ 所有测试通过！文件操作优化工作正常。")
            print("\n📋 优化总结:")
            print("  ✓ 异步文件复制、移动、删除")
            print("  ✓ 异步MD5计算")
            print("  ✓ 批量文件操作")
            print("  ✓ 进度回调支持")
            print("  ✓ 错误处理机制")
            print("  ✓ 向后兼容性")
        else:
            print("❌ 测试失败，请检查实现。")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
