"""
文件操作队列服务
专门处理文件相关的异步操作，避免阻塞主线程
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import uuid
from .common.async_file_utils import get_async_file_utils

logger = logging.getLogger(__name__)

class FileOperationType(Enum):
    """文件操作类型"""
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    CALCULATE_MD5 = "calculate_md5"
    BATCH_COPY = "batch_copy"
    BATCH_MOVE = "batch_move"
    BATCH_DELETE = "batch_delete"

class FileOperationStatus(Enum):
    """文件操作状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class FileOperation:
    """文件操作任务"""
    id: str
    operation_type: FileOperationType
    status: FileOperationStatus
    params: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress_callback: Optional[Callable] = None

class FileOperationQueue:
    """文件操作队列"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.queue = asyncio.Queue()
        self.running_operations: Dict[str, FileOperation] = {}
        self.completed_operations: Dict[str, FileOperation] = {}
        self.workers: List[asyncio.Task] = []
        self.is_running = False
        self.async_file_utils = get_async_file_utils()
        
    async def start(self):
        """启动队列服务"""
        if self.is_running:
            return
            
        self.is_running = True
        logger.info(f"启动文件操作队列，工作器数量: {self.max_workers}")
        
        # 启动工作器
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"file_worker_{i}"))
            self.workers.append(worker)
    
    async def stop(self):
        """停止队列服务"""
        self.is_running = False
        
        # 取消所有工作器
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作器完成
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("文件操作队列已停止")
    
    async def submit_operation(self, operation_type: FileOperationType, 
                              params: Dict[str, Any],
                              progress_callback: Optional[Callable] = None) -> str:
        """提交文件操作"""
        operation_id = str(uuid.uuid4())
        
        operation = FileOperation(
            id=operation_id,
            operation_type=operation_type,
            status=FileOperationStatus.PENDING,
            params=params,
            created_at=datetime.now(),
            progress_callback=progress_callback
        )
        
        await self.queue.put(operation)
        logger.info(f"提交文件操作: {operation_id} ({operation_type.value})")
        
        return operation_id
    
    async def get_operation_status(self, operation_id: str) -> Optional[FileOperation]:
        """获取操作状态"""
        if operation_id in self.running_operations:
            return self.running_operations[operation_id]
        elif operation_id in self.completed_operations:
            return self.completed_operations[operation_id]
        return None
    
    async def cancel_operation(self, operation_id: str) -> bool:
        """取消操作"""
        if operation_id in self.running_operations:
            operation = self.running_operations[operation_id]
            operation.status = FileOperationStatus.CANCELLED
            logger.info(f"取消文件操作: {operation_id}")
            return True
        return False
    
    async def _worker(self, worker_name: str):
        """工作器协程"""
        logger.info(f"文件操作工作器 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 获取操作任务
                operation = await asyncio.wait_for(self.queue.get(), timeout=1.0)
                
                # 执行操作
                await self._execute_operation(operation, worker_name)
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作器 {worker_name} 异常: {str(e)}")
        
        logger.info(f"文件操作工作器 {worker_name} 停止")
    
    async def _execute_operation(self, operation: FileOperation, worker_name: str):
        """执行文件操作"""
        operation_id = operation.id
        
        try:
            # 更新状态
            operation.status = FileOperationStatus.RUNNING
            operation.started_at = datetime.now()
            self.running_operations[operation_id] = operation
            
            logger.info(f"工作器 {worker_name} 开始执行操作: {operation_id}")
            
            # 根据操作类型执行相应的操作
            if operation.operation_type == FileOperationType.COPY:
                result = await self._handle_copy(operation)
            elif operation.operation_type == FileOperationType.MOVE:
                result = await self._handle_move(operation)
            elif operation.operation_type == FileOperationType.DELETE:
                result = await self._handle_delete(operation)
            elif operation.operation_type == FileOperationType.CALCULATE_MD5:
                result = await self._handle_calculate_md5(operation)
            elif operation.operation_type == FileOperationType.BATCH_COPY:
                result = await self._handle_batch_copy(operation)
            elif operation.operation_type == FileOperationType.BATCH_MOVE:
                result = await self._handle_batch_move(operation)
            elif operation.operation_type == FileOperationType.BATCH_DELETE:
                result = await self._handle_batch_delete(operation)
            else:
                raise ValueError(f"不支持的操作类型: {operation.operation_type}")
            
            # 操作成功
            operation.status = FileOperationStatus.COMPLETED
            operation.completed_at = datetime.now()
            operation.result = result
            
            logger.info(f"操作完成: {operation_id}")
            
        except Exception as e:
            # 操作失败
            operation.status = FileOperationStatus.FAILED
            operation.completed_at = datetime.now()
            operation.error = str(e)
            
            logger.error(f"操作失败: {operation_id} - {str(e)}")
        
        finally:
            # 移动到完成队列
            if operation_id in self.running_operations:
                del self.running_operations[operation_id]
            self.completed_operations[operation_id] = operation
    
    async def _handle_copy(self, operation: FileOperation) -> Dict[str, Any]:
        """处理文件复制"""
        source = operation.params["source"]
        target = operation.params["target"]
        
        success = await self.async_file_utils.copy_file_async(
            source, target, operation.progress_callback
        )
        
        return {"success": success, "source": source, "target": target}
    
    async def _handle_move(self, operation: FileOperation) -> Dict[str, Any]:
        """处理文件移动"""
        source = operation.params["source"]
        target = operation.params["target"]
        
        success = await self.async_file_utils.move_file_async(source, target)
        
        return {"success": success, "source": source, "target": target}
    
    async def _handle_delete(self, operation: FileOperation) -> Dict[str, Any]:
        """处理文件删除"""
        file_path = operation.params["file_path"]
        
        success = await self.async_file_utils.delete_file_async(file_path)
        
        return {"success": success, "file_path": file_path}
    
    async def _handle_calculate_md5(self, operation: FileOperation) -> Dict[str, Any]:
        """处理MD5计算"""
        file_path = operation.params["file_path"]
        
        md5_hash = await self.async_file_utils.calculate_md5_async(file_path)
        
        return {"success": md5_hash is not None, "file_path": file_path, "md5": md5_hash}
    
    async def _handle_batch_copy(self, operation: FileOperation) -> Dict[str, Any]:
        """处理批量复制"""
        operations = operation.params["operations"]
        max_concurrent = operation.params.get("max_concurrent", 5)
        
        result = await self.async_file_utils.batch_copy_files(
            operations, max_concurrent, operation.progress_callback
        )
        
        return result
    
    async def _handle_batch_move(self, operation: FileOperation) -> Dict[str, Any]:
        """处理批量移动"""
        operations = operation.params["operations"]
        max_concurrent = operation.params.get("max_concurrent", 5)
        
        result = await self.async_file_utils.batch_move_files(
            operations, max_concurrent, operation.progress_callback
        )
        
        return result
    
    async def _handle_batch_delete(self, operation: FileOperation) -> Dict[str, Any]:
        """处理批量删除"""
        file_paths = operation.params["file_paths"]
        max_concurrent = operation.params.get("max_concurrent", 5)
        
        result = await self.async_file_utils.batch_delete_files(
            file_paths, max_concurrent, operation.progress_callback
        )
        
        return result

# 全局实例
_file_operation_queue = None

async def get_file_operation_queue() -> FileOperationQueue:
    """获取文件操作队列实例"""
    global _file_operation_queue
    if _file_operation_queue is None:
        _file_operation_queue = FileOperationQueue()
        await _file_operation_queue.start()
    return _file_operation_queue
